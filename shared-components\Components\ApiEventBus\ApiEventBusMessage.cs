﻿using shared.Components.ApiEventBus;
using shared.Converters;
using shared.Models.Enums;
using System.Security.Claims;
using System.Text.Json.Serialization;

namespace shared.Components.APIEvents
{
    public class ApiEventBusMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Source { get; set; } = string.Empty;
        public ApiEvent ApiEvent { get; set; } = new ApiEvent();

        [JsonConverter(typeof(JsonEnumStringConverter<MicroserviceType>))]
        public MicroserviceType TargetMicroservice { get; set; }
        public int DelayInSeconds { get; set; } = 0;

        [JsonConverter(typeof(JsonListOfConverter<Claim, JsonClaimConverter>))]
        public List<Claim> Claims { get; set; } = new List<Claim>();

        public ApiEventBusMessage(ApiEvent apiEvent, MicroserviceType targetMicroservice, int delayInSeconds = 0, List<Claim>? additionalClaims = null)
        {
            Id = Guid.NewGuid().ToString();
            ApiEvent = apiEvent;
            TargetMicroservice = targetMicroservice;
            DelayInSeconds = delayInSeconds;
            Claims = additionalClaims ?? new List<Claim>();
        }

        public ApiEventBusMessage() { }
    }
}
