using Microsoft.Extensions.DependencyInjection;
using shared.Components.FlowBuilder;
using shared.Components.FlowBuilder.Extensions;
using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using shared.Components.FlowBuilder.Nodes;
using shared.Components.FlowBuilder.Tasks;
using shared.Models.Documents.DynamoDB;

namespace shared.Components.FlowBuilder.Examples
{
    /// <summary>
    /// Comprehensive example of how to use the FlowBuilder component.
    /// This example shows how to:
    /// 1. Register FlowBuilder services
    /// 2. Create custom nodes and tasks
    /// 3. Build and execute flows
    /// 4. Handle streaming results
    /// </summary>
    public class FlowBuilderUsageExample
    {
        /// <summary>
        /// Example of registering FlowBuilder services in dependency injection.
        /// </summary>
        public static void ConfigureServices(IServiceCollection services)
        {
            // Register FlowBuilder with in-memory state service
            services.AddFlowBuilder("ExampleFlowBuilder", useInMemoryState: true)
                    .RegisterNode<CustomEmailNode>("EmailNode")
                .RegisterTask<CustomHttpTask>("HttpTask")
                .RegisterTask<CustomDatabaseTask>("DatabaseTask")
                .ConfigureEventBus(eventBus =>
                {
                    // Configure event bus handlers
                    eventBus.Subscribe("flow_completed", async message =>
                    {
                        Console.WriteLine($"Flow completed: {message.Payload}");
                        return true;
                    });
                })
                .ConfigureResultDelivery(delivery =>
                {
                    // Configure result delivery handlers
                    delivery.OnDeliverySucceeded += (sender, args) =>
                    {
                        Console.WriteLine($"Result delivered to endpoint {args.EndpointId}");
                    };
                });

            // Register a second FlowBuilder instance for different use case
            services.AddNamedFlowBuilder("LLMBehaviorFlow", useInMemoryState: true)
                .RegisterNode<LLMReasoningNode>("LLMReasoning")
                .RegisterTask<LLMGenerationTask>("LLMGeneration");
        }

        /// <summary>
        /// Example of creating and executing a flow.
        /// </summary>
        public static async Task<FlowExecutionResult> ExecuteExampleFlowAsync(IFlowBuilder flowBuilder)
        {
            // Create a flow definition
            var flow = CreateExampleFlow();

            // Create execution context
            var context = new FlowExecutionContext
            {
                SessionId = Guid.NewGuid().ToString(),
                FlowId = flow.Id,
                AccountId = "example-account",
                InputFields = new Dictionary<string, object>
                {
                    ["userEmail"] = "<EMAIL>",
                    ["userName"] = "John Doe",
                    ["requestType"] = "support"
                },
                UserId = "example-user"
            };

            // Initialize field manager
            context.InitializeFieldManager(flow.Fields);

            // Execute the flow
            var result = await flowBuilder.ExecuteFlowAsync(flow.Id, context);

            return result;
        }

        /// <summary>
        /// Example of creating a flow definition programmatically.
        /// </summary>
        public static Flow CreateExampleFlow()
        {
            var flow = new Flow
            {
                Id = "example-flow-001",
                Name = "Customer Support Flow",
                Description = "Automated customer support workflow with email notifications",
                Version = "1.0.0"
            };

            // Define fields
            flow.Fields = new List<FlowField>
            {
                new FlowField
                {
                    Id = "field-001",
                    Name = "userEmail",
                    Type = FlowFieldType.Input,
                    DataType = FlowDataType.STRING,
                    Required = true
                },
                new FlowField
                {
                    Id = "field-002",
                    Name = "userName",
                    Type = FlowFieldType.Input,
                    DataType = FlowDataType.STRING,
                    Required = true
                },
                new FlowField
                {
                    Id = "field-003",
                    Name = "requestType",
                    Type = FlowFieldType.Input,
                    DataType = FlowDataType.STRING,
                    Required = true
                },
                new FlowField
                {
                    Id = "field-004",
                    Name = "ticketId",
                    Type = FlowFieldType.Variable,
                    DataType = FlowDataType.STRING,
                    Required = false
                },
                new FlowField
                {
                    Id = "field-005",
                    Name = "priority",
                    Type = FlowFieldType.Variable,
                    DataType = FlowDataType.INTEGER,
                    DefaultValue = "1"
                }
            };

            // Define nodes
            flow.Nodes = new List<FlowNode>
            {
                // Start node - Set Agent
                new FlowNode
                {
                    Id = "node-001",
                    Type = "SetAgent",
                    Name = "Set Support Agent",
                    Position = new FlowPosition { X = 100, Y = 100 },
                    Data = new FlowNodeData
                    {
                        BaseData = new FlowBaseNodeData
                        {
                            Name = "Set Support Agent"
                        },
                        NodeData = new Dictionary<string, object>
                        {
                            ["agentId"] = "support-agent-001",
                            ["isAlias"] = false
                        }
                    }
                },

                // Task node - Create ticket
                new FlowNode
                {
                    Id = "node-002",
                    Type = "Task",
                    Name = "Create Support Ticket",
                    Position = new FlowPosition { X = 300, Y = 100 },
                    Data = new FlowNodeData
                    {
                        BaseData = new FlowBaseNodeData
                        {
                            Name = "Create Support Ticket"
                        },
                        NodeData = new Dictionary<string, object>
                        {
                            ["tasks"] = new List<FlowTaskDefinition>
                            {
                                new FlowTaskDefinition
                                {
                                    Id = "task-001",
                                    Type = "DatabaseTask",
                                    Name = "Create Ticket Record",
                                    Configuration = new Dictionary<string, object>
                                    {
                                        ["query"] = "INSERT INTO tickets (user_email, user_name, request_type, priority) VALUES (?, ?, ?, ?)",
                                        ["parameters"] = new[] { "{userEmail}", "{userName}", "{requestType}", "{priority}" }
                                    }
                                }
                            }
                        }
                    }
                },

                // Switch node - Route based on priority
                new FlowNode
                {
                    Id = "node-003",
                    Type = "Switch",
                    Name = "Priority Router",
                    Position = new FlowPosition { X = 500, Y = 100 },
                    Data = new FlowNodeData
                    {
                        BaseData = new FlowBaseNodeData
                        {
                            Name = "Priority Router"
                        },
                        NodeData = new Dictionary<string, object>
                        {
                            ["conditions"] = new List<SwitchCondition>
                            {
                                new SwitchCondition
                                {
                                    Id = "condition-001",
                                    Label = "High Priority",
                                    Condition = new FlowRuleGroup
                                    {
                                        Combinator = "and",
                                        Rules = new List<FlowRule>
                                        {
                                            new FlowRule
                                            {
                                                Field = "priority",
                                                Operator = ">=",
                                                Value = 3
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },

                // Task node - Send high priority email
                new FlowNode
                {
                    Id = "node-004",
                    Type = "Task",
                    Name = "Send High Priority Email",
                    Position = new FlowPosition { X = 700, Y = 50 },
                    Data = new FlowNodeData
                    {
                        BaseData = new FlowBaseNodeData
                        {
                            Name = "Send High Priority Email"
                        },
                        NodeData = new Dictionary<string, object>
                        {
                            ["tasks"] = new List<FlowTaskDefinition>
                            {
                                new FlowTaskDefinition
                                {
                                    Id = "task-002",
                                    Type = "EmailNode",
                                    Name = "Send Urgent Notification",
                                    Configuration = new Dictionary<string, object>
                                    {
                                        ["to"] = "{userEmail}",
                                        ["subject"] = "Urgent: Your support request has been received",
                                        ["template"] = "urgent_support_template",
                                        ["variables"] = new Dictionary<string, object>
                                        {
                                            ["userName"] = "{userName}",
                                            ["ticketId"] = "{ticketId}"
                                        }
                                    }
                                }
                            }
                        }
                    }
                },

                // Task node - Send standard email
                new FlowNode
                {
                    Id = "node-005",
                    Type = "Task",
                    Name = "Send Standard Email",
                    Position = new FlowPosition { X = 700, Y = 150 },
                    Data = new FlowNodeData
                    {
                        BaseData = new FlowBaseNodeData
                        {
                            Name = "Send Standard Email"
                        },
                        NodeData = new Dictionary<string, object>
                        {
                            ["tasks"] = new List<FlowTaskDefinition>
                            {
                                new FlowTaskDefinition
                                {
                                    Id = "task-003",
                                    Type = "EmailNode",
                                    Name = "Send Standard Notification",
                                    Configuration = new Dictionary<string, object>
                                    {
                                        ["to"] = "{userEmail}",
                                        ["subject"] = "Your support request has been received",
                                        ["template"] = "standard_support_template",
                                        ["variables"] = new Dictionary<string, object>
                                        {
                                            ["userName"] = "{userName}",
                                            ["ticketId"] = "{ticketId}"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            // Define edges (connections between nodes)
            flow.Edges = new List<FlowEdge>
            {
                new FlowEdge
                {
                    Id = "edge-001",
                    Source = "node-001",
                    Target = "node-002",
                    SourceHandle = "output",
                    TargetHandle = "trigger"
                },
                new FlowEdge
                {
                    Id = "edge-002",
                    Source = "node-002",
                    Target = "node-003",
                    SourceHandle = "next",
                    TargetHandle = "input"
                },
                new FlowEdge
                {
                    Id = "edge-003",
                    Source = "node-003",
                    Target = "node-004",
                    SourceHandle = "condition-0",
                    TargetHandle = "trigger"
                },
                new FlowEdge
                {
                    Id = "edge-004",
                    Source = "node-003",
                    Target = "node-005",
                    SourceHandle = "default",
                    TargetHandle = "trigger"
                }
            };

            return flow;
        }
    }

    // Example custom nodes and tasks

    [FlowNodeType("EmailNode", Description = "Sends email notifications", Category = "Communication")]
    public class CustomEmailNode : BaseFlowNode
    {
        public CustomEmailNode()
        {
            NodeType = "EmailNode";
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            // Implementation would send email
            await Task.Delay(100, cancellationToken);
            return CreateSuccessResult();
        }
    }

    [FlowTaskType("HttpTask", Description = "Makes HTTP requests", Category = "Integration", SupportsStreaming = true)]
    public class CustomHttpTask : BaseFlowTask
    {
        public CustomHttpTask()
        {
            TaskType = "HttpTask";
            SupportsStreaming = true;
        }

        public override async Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            FireStreamResult("progress", "Starting HTTP request");
            await Task.Delay(500, cancellationToken);
            FireStreamResult("completed", "HTTP request completed");
            return CreateSuccessResult();
        }
    }

    [FlowTaskType("DatabaseTask", Description = "Executes database operations", Category = "Data")]
    public class CustomDatabaseTask : BaseFlowTask
    {
        public CustomDatabaseTask()
        {
            TaskType = "DatabaseTask";
        }

        public override async Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            // Implementation would execute database query
            await Task.Delay(200, cancellationToken);
            
            // Set ticket ID as a result
            context.SetFieldValue("ticketId", $"TICKET-{DateTime.UtcNow:yyyyMMdd}-{Random.Shared.Next(1000, 9999)}");
            
            return CreateSuccessResult(new Dictionary<string, object>
            {
                ["recordsAffected"] = 1,
                ["ticketId"] = context.GetFieldValue("ticketId")!
            });
        }
    }

    [FlowNodeType("LLMReasoning", Description = "LLM reasoning node", Category = "AI")]
    public class LLMReasoningNode : BaseFlowNode
    {
        public LLMReasoningNode()
        {
            NodeType = "LLMReasoning";
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            // Implementation would perform LLM reasoning
            await Task.Delay(1000, cancellationToken);
            return CreateSuccessResult();
        }
    }

    [FlowTaskType("LLMGeneration", Description = "LLM text generation", Category = "AI", IsLongRunning = true, SupportsStreaming = true)]
    public class LLMGenerationTask : BaseFlowTask
    {
        public LLMGenerationTask()
        {
            TaskType = "LLMGeneration";
            IsLongRunning = true;
            SupportsStreaming = true;
        }

        public override async Task<TaskExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            FireStreamResult("progress", "Starting LLM generation");
            await Task.Delay(2000, cancellationToken);
            FireStreamResult("completed", "LLM generation completed");
            return CreateSuccessResult();
        }
    }
}
