<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>shared</RootNamespace>
    <UserSecretsId>d344d291-8d16-47b8-8441-15f567d9e167</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AWSSDK.Core" Version="4.0.0.16" />
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="4.0.2.3" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="4.0.2" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="4.0.0.14" />
    <PackageReference Include="AWSSDK.SecretsManager.Caching" Version="2.0.0" />
    <PackageReference Include="AWSSDK.SQS" Version="4.0.0.15" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\Request\" />
  </ItemGroup>

</Project>
