using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Set Agent Node - Configures which agent to use for execution.
    /// Has 1 input (target handle on left) and 1 output (source handle on right).
    /// </summary>
    public class SetAgentNode : BaseFlowNode
    {
        private SetAgentNodeDefinition? _nodeConfig;

        public SetAgentNode()
        {
            NodeType = "SetAgent";
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Single output handle on the right
            _outputHandles.Add(new NodeHandle
            {
                Id = "output",
                Name = "Output",
                Type = NodeHandleType.Output,
                Position = "right"
            });

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new SetAgentNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata,
                    AgentId = definition.GetConfigValue<string>("agentId", ""),
                    IsAlias = definition.GetConfigValue<bool>("isAlias", false),
                    AgentAliasOrTag = definition.GetConfigValue<string>("agentAliasOrTag", "")
                };

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate agent configuration
            if (string.IsNullOrEmpty(_nodeConfig.AgentId))
            {
                result.Errors.Add("Agent ID is required");
            }

            if (_nodeConfig.IsAlias && string.IsNullOrEmpty(_nodeConfig.AgentAliasOrTag))
            {
                result.Errors.Add("Agent alias or tag is required when IsAlias is true");
            }

            // Additional validation could include checking if the agent exists
            // This would require access to an agent service/repository
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                // Set agent information in the execution context
                var outputData = new Dictionary<string, object>
                {
                    ["agentId"] = _nodeConfig.AgentId,
                    ["isAlias"] = _nodeConfig.IsAlias,
                    ["agentAliasOrTag"] = _nodeConfig.AgentAliasOrTag
                };

                // Store agent information in execution data for use by subsequent nodes
                context.ExecutionData["currentAgentId"] = _nodeConfig.AgentId;
                context.ExecutionData["currentAgentIsAlias"] = _nodeConfig.IsAlias;
                context.ExecutionData["currentAgentAliasOrTag"] = _nodeConfig.AgentAliasOrTag;

                // Set variable fields if they exist
                context.SetFieldValue("agent.id", _nodeConfig.AgentId);
                context.SetFieldValue("agent.isAlias", _nodeConfig.IsAlias);
                context.SetFieldValue("agent.aliasOrTag", _nodeConfig.AgentAliasOrTag);

                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing SetAgent node: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the configured agent information.
        /// </summary>
        public (string AgentId, bool IsAlias, string AgentAliasOrTag) GetAgentInfo()
        {
            if (_nodeConfig == null)
                return ("", false, "");

            return (_nodeConfig.AgentId, _nodeConfig.IsAlias, _nodeConfig.AgentAliasOrTag);
        }
    }
}
