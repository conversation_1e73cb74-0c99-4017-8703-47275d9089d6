# FlowBuilder Component

A comprehensive, interface-based FlowBuilder system for .NET applications that supports dependency injection, async execution, streaming capabilities, and multiple FlowBuilder instances.

## Features

- **Interface-based design** for dependency injection and testability
- **Async execution** for nodes and tasks with cancellation support
- **Streaming capabilities** for intermediate results and real-time updates
- **Generic Event Bus** for long-running tasks and inter-component communication
- **Service interfaces** for flow state storage (compatible with NoSQL and memory databases)
- **Multiple FlowBuilder instances** support for different use cases
- **Custom node and task registration** with automatic discovery
- **Field system** with type validation and variable management
- **Result delivery** to various endpoints (webhooks, websockets, message queues)

## Architecture

### Core Components

1. **IFlowBuilder** - Main interface for flow management and execution
2. **IFlowEngine** - Core engine for executing flows and managing state
3. **IFlowNode** - Base interface for all flow nodes
4. **IFlowTask** - Base interface for all flow tasks
5. **IFlowStateService** - Interface for flow state persistence
6. **IGenericEventBus** - Generic event bus for messaging
7. **IFlowResultDelivery** - Interface for delivering results to endpoints

### Node Types

- **SetAgent** - Configures which agent to use for execution
- **Task** - Executes specific tasks with configurable parameters
- **Sequence** - Executes child nodes in sequential order
- **Parallel** - Executes child nodes simultaneously
- **Select** - Chooses between multiple execution paths
- **Loop** - Repeats execution based on conditions
- **Switch** - Conditional branching with multiple conditions

### Task Types

- **DummyTask** - Simple demonstration task for testing
- **ConditionalTask** - Complex conditional logic with IF/ELSE branches
- **LambdaTask** - AWS Lambda function execution
- **HttpRequestTask** - HTTP API integration
- **DatabaseQueryTask** - Database operations
- **MessageQueueTask** - Message queue integration

## Quick Start

### 1. Register Services

```csharp
using shared.Components.FlowBuilder.Extensions;

// In your Startup.cs or Program.cs
services.AddFlowBuilder("MyFlowBuilder", useInMemoryState: true)
    .RegisterCustomNode<MyCustomNode>("MyNode")
    .RegisterCustomTask<MyCustomTask>("MyTask")
    .ConfigureEventBus(eventBus =>
    {
        eventBus.Subscribe("flow_completed", async message =>
        {
            Console.WriteLine($"Flow completed: {message.Payload}");
            return true;
        });
    });
```

### 2. Create a Flow

```csharp
var flow = new Flow
{
    Id = "my-flow-001",
    Name = "My Example Flow",
    Description = "An example workflow",
    Fields = new List<FlowField>
    {
        new FlowField
        {
            Id = "field-001",
            Name = "userInput",
            Type = FlowFieldType.Input,
            DataType = FlowDataType.STRING,
            Required = true
        }
    },
    Nodes = new List<FlowNode>
    {
        new FlowNode
        {
            Id = "node-001",
            Type = "SetAgent",
            Name = "Set Agent",
            Data = new FlowNodeData
            {
                NodeData = new Dictionary<string, object>
                {
                    ["agentId"] = "my-agent-001"
                }
            }
        }
    }
};
```

### 3. Execute the Flow

```csharp
var context = new FlowExecutionContext
{
    SessionId = Guid.NewGuid().ToString(),
    FlowId = flow.Id,
    AccountId = "my-account",
    InputFields = new Dictionary<string, object>
    {
        ["userInput"] = "Hello, World!"
    }
};

context.InitializeFieldManager(flow.Fields);

var result = await flowBuilder.ExecuteFlowAsync(flow.Id, context);
```

## Custom Nodes and Tasks

### Creating a Custom Node

```csharp
[FlowNodeType("MyCustomNode", Description = "My custom node", Category = "Custom")]
public class MyCustomNode : BaseFlowNode
{
    public MyCustomNode()
    {
        NodeType = "MyCustomNode";
    }

    public override async Task<NodeExecutionResult> ExecuteAsync(
        FlowExecutionContext context, 
        CancellationToken cancellationToken = default)
    {
        // Your custom logic here
        await Task.Delay(100, cancellationToken);
        
        return CreateSuccessResult(new Dictionary<string, object>
        {
            ["result"] = "Custom node executed successfully"
        });
    }
}
```

### Creating a Custom Task

```csharp
[FlowTaskType("MyCustomTask", 
    Description = "My custom task", 
    Category = "Custom",
    SupportsStreaming = true)]
public class MyCustomTask : BaseFlowTask
{
    public MyCustomTask()
    {
        TaskType = "MyCustomTask";
        SupportsStreaming = true;
    }

    public override async Task<TaskExecutionResult> ExecuteAsync(
        FlowExecutionContext context, 
        CancellationToken cancellationToken = default)
    {
        FireStreamResult("progress", "Starting custom task");
        
        // Your custom logic here
        await Task.Delay(500, cancellationToken);
        
        FireStreamResult("completed", "Custom task completed");
        
        return CreateSuccessResult(new Dictionary<string, object>
        {
            ["result"] = "Custom task executed successfully"
        });
    }
}
```

## Field System

The FlowBuilder includes a comprehensive field system for managing input and variable fields:

```csharp
// Define fields in your flow
var fields = new List<FlowField>
{
    new FlowField
    {
        Name = "userEmail",
        Type = FlowFieldType.Input,
        DataType = FlowDataType.STRING,
        Required = true
    },
    new FlowField
    {
        Name = "processedData",
        Type = FlowFieldType.Variable,
        DataType = FlowDataType.JSON,
        DefaultValue = "{}"
    }
};

// Use fields in execution context
context.InitializeFieldManager(fields);

// Get field values with type safety
var email = context.GetFieldValue<string>("userEmail");
var isValid = context.GetFieldValue<bool>("isValid", false);

// Set variable fields
context.SetFieldValue("processedData", new { status = "completed" });

// Substitute variables in templates
var message = context.SubstituteVariables("Hello {userName}, your email {userEmail} has been processed.");
```

## State Management

FlowBuilder supports both in-memory and NoSQL state management:

### In-Memory State Service
```csharp
services.AddFlowBuilder("MyFlowBuilder", useInMemoryState: true);
```

### NoSQL State Service (DynamoDB)
```csharp
services.AddFlowBuilder("MyFlowBuilder", useInMemoryState: false);
// Requires proper DynamoDB repository configuration
```

## Event Bus

The Generic Event Bus can be used by any component in your system:

```csharp
// Subscribe to events
eventBus.Subscribe("user_registered", async message =>
{
    var userData = message.Payload;
    // Process user registration
    return true;
});

// Send events
await eventBus.SendMessageAsync(new GenericEventMessage
{
    MessageType = "user_registered",
    Source = "UserService",
    Payload = new { UserId = "123", Email = "<EMAIL>" }
});
```

## Result Delivery

Configure result delivery to various endpoints:

```csharp
// Register delivery endpoints
await resultDelivery.RegisterDeliveryEndpointAsync(sessionId, new FlowDeliveryEndpoint
{
    Type = FlowDeliveryType.Webhook,
    Url = "https://api.example.com/webhook",
    DeliverResults = true,
    DeliverStreaming = true
});

// Results will be automatically delivered when flow completes
```

## Multiple FlowBuilder Instances

Support for multiple FlowBuilder instances for different use cases:

```csharp
// Register different FlowBuilder instances
services.AddFlowBuilder("GeneralPurpose", useInMemoryState: true)
    .RegisterCustomNode<StandardNode>("Standard");

services.AddNamedFlowBuilder("LLMBehaviorFlow", useInMemoryState: true)
    .RegisterCustomNode<LLMReasoningNode>("LLMReasoning")
    .RegisterCustomTask<LLMGenerationTask>("LLMGeneration");
```

## Best Practices

1. **Use interface-based design** for dependency injection and testability
2. **Implement proper validation** in custom nodes and tasks
3. **Handle cancellation tokens** for responsive cancellation
4. **Use streaming** for long-running operations
5. **Validate field types** and handle conversion errors
6. **Use the event bus** for decoupled communication
7. **Configure appropriate state storage** based on your requirements
8. **Test custom components** thoroughly before deployment

## Examples

See the `Examples/FlowBuilderUsageExample.cs` file for comprehensive usage examples including:
- Service registration
- Flow creation
- Custom nodes and tasks
- Field management
- Event handling
- Result delivery

## Dependencies

- Microsoft.Extensions.DependencyInjection
- System.Threading.Channels
- System.Text.Json
- Amazon.DynamoDBv2 (for NoSQL state service)
- Amazon.Lambda (for Lambda task)

## License

This component is part of the shared components library and follows the same licensing terms as the parent project.
