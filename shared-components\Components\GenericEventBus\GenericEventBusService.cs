using shared.Components.GenericEventBus.Interfaces;
using shared.Components.GenericEventBus.Models;
using System.Collections.Concurrent;
using System.Threading.Channels;

namespace shared.Components.GenericEventBus
{
    /// <summary>
    /// Generic event bus implementation for handling events and message queuing across the application.
    /// This is a shared component that can be used by any part of the system.
    /// </summary>
    public class GenericEventBusService : IGenericEventBus, IDisposable
    {
        private readonly ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>> _messageHandlers;
        private readonly ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>> _queueHandlers;
        private readonly ConcurrentDictionary<string, Channel<GenericEventMessage>> _queues;
        private readonly ConcurrentDictionary<string, EventBusQueueConfiguration> _queueConfigurations;
        private readonly ConcurrentDictionary<string, MessageStatus> _messageStatuses;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Task _processingTask;
        private readonly SemaphoreSlim _processingLock;
        private readonly EventBusStatistics _statistics;

        public event EventHandler<MessageProcessedEventArgs>? OnMessageProcessed;
        public event EventHandler<MessageFailedEventArgs>? OnMessageFailed;

        public GenericEventBusService()
        {
            _messageHandlers = new ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>>();
            _queueHandlers = new ConcurrentDictionary<string, List<Func<GenericEventMessage, Task<bool>>>>();
            _queues = new ConcurrentDictionary<string, Channel<GenericEventMessage>>();
            _queueConfigurations = new ConcurrentDictionary<string, EventBusQueueConfiguration>();
            _messageStatuses = new ConcurrentDictionary<string, MessageStatus>();
            _cancellationTokenSource = new CancellationTokenSource();
            _processingLock = new SemaphoreSlim(1, 1);
            _statistics = new EventBusStatistics();

            // Create default queue with default configuration
            CreateQueue("default", new EventBusQueueConfiguration { Name = "default" });

            // Start background processing
            _processingTask = Task.Run(ProcessMessagesAsync);
        }

        public async Task<string> SendMessageAsync(GenericEventMessage message, int delayInSeconds = 0)
        {
            if (string.IsNullOrEmpty(message.Id))
            {
                message.Id = Guid.NewGuid().ToString();
            }

            // Set processing time if delay is specified
            if (delayInSeconds > 0)
            {
                message.ProcessAt = DateTime.UtcNow.AddSeconds(delayInSeconds);
            }

            // Update message status and statistics
            _messageStatuses[message.Id] = MessageStatus.Pending;
            Interlocked.Increment(ref _statistics.TotalMessagesSent);
            
            // Update message type statistics
            _statistics.MessageTypeStats.AddOrUpdate(message.MessageType, 1, (key, value) => value + 1);

            // Send to default queue
            await SendToQueueAsync("default", message, 0);

            return message.Id;
        }

        public async Task<string> SendToQueueAsync(string queueName, GenericEventMessage message, int delayInSeconds = 0)
        {
            if (string.IsNullOrEmpty(message.Id))
            {
                message.Id = Guid.NewGuid().ToString();
            }

            // Set processing time if delay is specified
            if (delayInSeconds > 0)
            {
                message.ProcessAt = DateTime.UtcNow.AddSeconds(delayInSeconds);
            }

            // Ensure queue exists
            if (!_queues.ContainsKey(queueName))
            {
                CreateQueue(queueName, new EventBusQueueConfiguration { Name = queueName });
            }

            // Update message status and statistics
            _messageStatuses[message.Id] = MessageStatus.Pending;
            Interlocked.Increment(ref _statistics.TotalMessagesSent);
            
            // Update queue statistics
            _statistics.QueueStats.AddOrUpdate(queueName, 1, (key, value) => value + 1);

            // Check TTL
            if (message.TimeToLive.HasValue)
            {
                var expiry = message.CreatedAt.Add(message.TimeToLive.Value);
                if (DateTime.UtcNow > expiry)
                {
                    _messageStatuses[message.Id] = MessageStatus.Expired;
                    Interlocked.Increment(ref _statistics.TotalMessagesExpired);
                    return message.Id;
                }
            }

            // Add to queue
            var queue = _queues[queueName];
            await queue.Writer.WriteAsync(message, _cancellationTokenSource.Token);

            return message.Id;
        }

        public void Subscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler)
        {
            _messageHandlers.AddOrUpdate(messageType,
                new List<Func<GenericEventMessage, Task<bool>>> { handler },
                (key, existing) =>
                {
                    existing.Add(handler);
                    return existing;
                });
        }

        public void SubscribeToQueue(string queueName, Func<GenericEventMessage, Task<bool>> handler)
        {
            _queueHandlers.AddOrUpdate(queueName,
                new List<Func<GenericEventMessage, Task<bool>>> { handler },
                (key, existing) =>
                {
                    existing.Add(handler);
                    return existing;
                });
        }

        public void Unsubscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler)
        {
            if (_messageHandlers.TryGetValue(messageType, out var handlers))
            {
                handlers.Remove(handler);
                if (handlers.Count == 0)
                {
                    _messageHandlers.TryRemove(messageType, out _);
                }
            }
        }

        public async Task StartAsync(CancellationToken cancellationToken = default)
        {
            // Already started in constructor
            await Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken = default)
        {
            _cancellationTokenSource.Cancel();
            
            // Close all queue writers
            foreach (var queue in _queues.Values)
            {
                queue.Writer.Complete();
            }

            try
            {
                await _processingTask;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        public async Task<MessageStatus> GetMessageStatusAsync(string messageId)
        {
            await Task.CompletedTask;
            return _messageStatuses.GetValueOrDefault(messageId, MessageStatus.Pending);
        }

        /// <summary>
        /// Get event bus statistics.
        /// </summary>
        public EventBusStatistics GetStatistics()
        {
            return new EventBusStatistics
            {
                TotalMessagesSent = _statistics.TotalMessagesSent,
                TotalMessagesProcessed = _statistics.TotalMessagesProcessed,
                TotalMessagesFailed = _statistics.TotalMessagesFailed,
                TotalMessagesRetried = _statistics.TotalMessagesRetried,
                TotalMessagesExpired = _statistics.TotalMessagesExpired,
                MessageTypeStats = new Dictionary<string, long>(_statistics.MessageTypeStats),
                QueueStats = new Dictionary<string, long>(_statistics.QueueStats),
                AverageProcessingTime = _statistics.AverageProcessingTime,
                LastResetTime = _statistics.LastResetTime
            };
        }

        /// <summary>
        /// Get health status of the event bus.
        /// </summary>
        public EventBusHealthStatus GetHealthStatus()
        {
            var issues = new List<string>();
            var details = new Dictionary<string, object>();

            // Check if processing task is running
            if (_processingTask.IsCompleted || _processingTask.IsFaulted || _processingTask.IsCanceled)
            {
                issues.Add("Processing task is not running");
            }

            // Check queue health
            foreach (var queueKvp in _queues)
            {
                var queueName = queueKvp.Key;
                var queue = queueKvp.Value;
                
                details[$"queue_{queueName}_reader_completion"] = queue.Reader.Completion.IsCompleted;
                details[$"queue_{queueName}_writer_completion"] = queue.Writer.TryComplete();
            }

            details["total_queues"] = _queues.Count;
            details["total_message_handlers"] = _messageHandlers.Count;
            details["total_queue_handlers"] = _queueHandlers.Count;
            details["active_messages"] = _messageStatuses.Count(kvp => kvp.Value == MessageStatus.Processing);

            return new EventBusHealthStatus
            {
                IsHealthy = issues.Count == 0,
                Status = issues.Count == 0 ? "Healthy" : "Unhealthy",
                Details = details,
                Issues = issues
            };
        }

        /// <summary>
        /// Create a new queue with configuration.
        /// </summary>
        public void CreateQueue(string queueName, EventBusQueueConfiguration configuration)
        {
            var options = new BoundedChannelOptions(configuration.MaxSize)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = configuration.MaxConcurrency == 1,
                SingleWriter = false
            };

            var channel = Channel.CreateBounded<GenericEventMessage>(options);
            _queues[queueName] = channel;
            _queueConfigurations[queueName] = configuration;
        }

        /// <summary>
        /// Reset statistics.
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.TotalMessagesSent = 0;
            _statistics.TotalMessagesProcessed = 0;
            _statistics.TotalMessagesFailed = 0;
            _statistics.TotalMessagesRetried = 0;
            _statistics.TotalMessagesExpired = 0;
            _statistics.MessageTypeStats.Clear();
            _statistics.QueueStats.Clear();
            _statistics.AverageProcessingTime = TimeSpan.Zero;
            _statistics.LastResetTime = DateTime.UtcNow;
        }

        /// <summary>
        /// Background task for processing messages from all queues.
        /// </summary>
        private async Task ProcessMessagesAsync()
        {
            var tasks = new List<Task>();

            try
            {
                // Start processing tasks for each queue
                foreach (var queueKvp in _queues)
                {
                    var queueName = queueKvp.Key;
                    var queue = queueKvp.Value;
                    
                    tasks.Add(ProcessQueueAsync(queueName, queue));
                }

                // Wait for all processing tasks to complete
                await Task.WhenAll(tasks);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        /// <summary>
        /// Process messages from a specific queue.
        /// </summary>
        private async Task ProcessQueueAsync(string queueName, Channel<GenericEventMessage> queue)
        {
            try
            {
                await foreach (var message in queue.Reader.ReadAllAsync(_cancellationTokenSource.Token))
                {
                    // Check TTL
                    if (message.TimeToLive.HasValue)
                    {
                        var expiry = message.CreatedAt.Add(message.TimeToLive.Value);
                        if (DateTime.UtcNow > expiry)
                        {
                            _messageStatuses[message.Id] = MessageStatus.Expired;
                            Interlocked.Increment(ref _statistics.TotalMessagesExpired);
                            continue;
                        }
                    }

                    // Check if message should be delayed
                    if (message.ProcessAt.HasValue && message.ProcessAt.Value > DateTime.UtcNow)
                    {
                        // Re-queue the message with delay
                        var delay = message.ProcessAt.Value - DateTime.UtcNow;
                        _ = Task.Delay(delay, _cancellationTokenSource.Token)
                            .ContinueWith(async _ =>
                            {
                                if (!_cancellationTokenSource.Token.IsCancellationRequested)
                                {
                                    await queue.Writer.WriteAsync(message, _cancellationTokenSource.Token);
                                }
                            }, _cancellationTokenSource.Token);
                        continue;
                    }

                    await ProcessMessageAsync(queueName, message);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
        }

        /// <summary>
        /// Process a single message.
        /// </summary>
        private async Task ProcessMessageAsync(string queueName, GenericEventMessage message)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                _messageStatuses[message.Id] = MessageStatus.Processing;

                var processed = false;

                // Try queue-specific handlers first
                if (_queueHandlers.TryGetValue(queueName, out var queueHandlers))
                {
                    foreach (var handler in queueHandlers.ToList())
                    {
                        try
                        {
                            if (await handler(message))
                            {
                                processed = true;
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            await HandleMessageFailure(message, ex, startTime);
                            return;
                        }
                    }
                }

                // Try message type handlers if not processed by queue handlers
                if (!processed && _messageHandlers.TryGetValue(message.MessageType, out var messageHandlers))
                {
                    foreach (var handler in messageHandlers.ToList())
                    {
                        try
                        {
                            if (await handler(message))
                            {
                                processed = true;
                                break;
                            }
                        }
                        catch (Exception ex)
                        {
                            await HandleMessageFailure(message, ex, startTime);
                            return;
                        }
                    }
                }

                if (processed)
                {
                    _messageStatuses[message.Id] = MessageStatus.Completed;
                    Interlocked.Increment(ref _statistics.TotalMessagesProcessed);
                    
                    var processingTime = DateTime.UtcNow - startTime;
                    
                    OnMessageProcessed?.Invoke(this, new MessageProcessedEventArgs
                    {
                        MessageId = message.Id,
                        MessageType = message.MessageType,
                        Source = message.Source,
                        Target = message.Target,
                        ProcessingTime = processingTime,
                        Metadata = message.Metadata
                    });
                }
                else
                {
                    // No handler processed the message
                    await HandleMessageFailure(message, new InvalidOperationException("No handler processed the message"), startTime);
                }
            }
            catch (Exception ex)
            {
                await HandleMessageFailure(message, ex, startTime);
            }
        }

        /// <summary>
        /// Handle message processing failure.
        /// </summary>
        private async Task HandleMessageFailure(GenericEventMessage message, Exception exception, DateTime startTime)
        {
            message.RetryCount++;
            Interlocked.Increment(ref _statistics.TotalMessagesFailed);
            
            var willRetry = message.RetryCount < message.MaxRetries;
            
            if (willRetry)
            {
                _messageStatuses[message.Id] = MessageStatus.Retrying;
                Interlocked.Increment(ref _statistics.TotalMessagesRetried);
                
                // Re-queue with exponential backoff
                var delay = TimeSpan.FromSeconds(Math.Pow(2, message.RetryCount));
                message.ProcessAt = DateTime.UtcNow.Add(delay);
                
                // Send back to the same queue
                var queueName = "default"; // Default queue for retries
                if (_queues.TryGetValue(queueName, out var queue))
                {
                    await queue.Writer.WriteAsync(message, _cancellationTokenSource.Token);
                }
            }
            else
            {
                _messageStatuses[message.Id] = MessageStatus.Failed;
            }

            OnMessageFailed?.Invoke(this, new MessageFailedEventArgs
            {
                MessageId = message.Id,
                MessageType = message.MessageType,
                Source = message.Source,
                Target = message.Target,
                Exception = exception,
                RetryCount = message.RetryCount,
                WillRetry = willRetry,
                Metadata = message.Metadata
            });
        }

        public void Dispose()
        {
            _cancellationTokenSource.Cancel();
            
            try
            {
                _processingTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (AggregateException)
            {
                // Ignore timeout exceptions during disposal
            }

            _cancellationTokenSource.Dispose();
            _processingLock.Dispose();
        }
    }
}
