using Amazon.DynamoDBv2.DataModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shared.Components.ApiEventBus;
using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using shared.Controllers;
using shared.Models.Documents.DynamoDB;
using shared.Models.Response;
using System.ComponentModel.DataAnnotations;

namespace shared.Components.FlowBuilder.Controllers
{
    /// <summary>
    /// Base controller for flow CRUD operations. Extends SuperController and provides
    /// full CRUD operations for flows. Designed to be inheritable for specific flow types.
    /// </summary>
    [ApiController]
    public abstract class BaseFlowController : SuperController
    {
        protected readonly IFlowBuilder _flowBuilder;
        protected readonly IFlowEngine _flowEngine;
        protected readonly IFlowStateService _stateService;
        protected readonly IFlowResultDelivery _resultDelivery;

        protected BaseFlowController(
            IApiEventBus eventBus,
            IDynamoDBContext dbContext,
            IFlow<PERSON><PERSON><PERSON> flowBuilder,
            IFlowEngine flowEngine,
            IFlowStateService stateService,
            IFlowResultDelivery resultDelivery) : base(eventBus, dbContext)
        {
            _flowBuilder = flowBuilder;
            _flowEngine = flowEngine;
            _stateService = stateService;
            _resultDelivery = resultDelivery;
        }

        /// <summary>
        /// Get all flows for the authenticated user.
        /// </summary>
        [HttpGet]
        [Authorize]
        public virtual async Task<IActionResult> GetFlows([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                var accountId = GetAccountId();
                if (string.IsNullOrEmpty(accountId))
                    return BadRequest("Account ID is required");

                var flows = await GetDBEntriesByGSI<FlowDocument>(
                    FlowDocument.AccountIdIdIndex,
                    nameof(FlowDocument.AccountId),
                    accountId,
                    null,
                    null,
                    pageSize,
                    page > 1 ? ((page - 1) * pageSize).ToString() : null);

                var response = flows.Entries.Select(f => new
                {
                    id = f.Id,
                    flowId = f.FlowId,
                    name = f.Name,
                    description = f.Description,
                    flowType = f.FlowType,
                    version = f.Version,
                    isActive = f.IsActive,
                    createdAt = f.CreatedAt,
                    lastChangeTimestamp = f.LastChangeTimestamp
                }).ToList();

                return Ok(new ListResponse<object>
                {
                    Entries = response,
                    NextToken = flows.NextToken,
                    Total = flows.Total
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error retrieving flows: {ex.Message}");
            }
        }

        /// <summary>
        /// Get a specific flow by ID.
        /// </summary>
        [HttpGet("{flowId}")]
        [Authorize]
        public virtual async Task<IActionResult> GetFlow(string flowId)
        {
            try
            {
                var accountId = GetAccountId();
                if (string.IsNullOrEmpty(accountId))
                    return BadRequest("Account ID is required");

                var flowDoc = await GetDBEntry<FlowDocument>(accountId, flowId);
                if (flowDoc == null)
                    return NotFound("Flow not found");

                return Ok(flowDoc);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error retrieving flow: {ex.Message}");
            }
        }

        /// <summary>
        /// Create a new flow.
        /// </summary>
        [HttpPost]
        [Authorize]
        public virtual async Task<IActionResult> CreateFlow([FromBody] CreateFlowRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var accountId = GetAccountId();
                if (string.IsNullOrEmpty(accountId))
                    return BadRequest("Account ID is required");

                // Validate flow definition
                var validationResult = await _flowBuilder.ValidateFlowAsync(request.FlowDefinition);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new
                    {
                        message = "Flow validation failed",
                        errors = validationResult.Errors,
                        warnings = validationResult.Warnings
                    });
                }

                var flowDoc = new FlowDocument
                {
                    AccountId = accountId,
                    FlowId = Guid.NewGuid().ToString(),
                    Id = Guid.NewGuid().ToString(),
                    Name = request.Name,
                    Description = request.Description,
                    FlowType = GetFlowType(),
                    Version = request.Version ?? "1.0.0",
                    IsActive = request.IsActive ?? true,
                    FlowDefinition = request.FlowDefinition
                };

                var savedFlow = await PutDBEntry(flowDoc);
                if (savedFlow == null)
                    return StatusCode(500, "Failed to create flow");

                return CreatedAtAction(nameof(GetFlow), new { flowId = savedFlow.FlowId }, savedFlow);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error creating flow: {ex.Message}");
            }
        }

        /// <summary>
        /// Update an existing flow.
        /// </summary>
        [HttpPut("{flowId}")]
        [Authorize]
        public virtual async Task<IActionResult> UpdateFlow(string flowId, [FromBody] UpdateFlowRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var accountId = GetAccountId();
                if (string.IsNullOrEmpty(accountId))
                    return BadRequest("Account ID is required");

                var flowDoc = await GetDBEntry<FlowDocument>(accountId, flowId);
                if (flowDoc == null)
                    return NotFound("Flow not found");

                // Update fields
                if (!string.IsNullOrEmpty(request.Name))
                    flowDoc.Name = request.Name;

                if (!string.IsNullOrEmpty(request.Description))
                    flowDoc.Description = request.Description;

                if (!string.IsNullOrEmpty(request.Version))
                    flowDoc.Version = request.Version;

                if (request.IsActive.HasValue)
                    flowDoc.IsActive = request.IsActive.Value;

                if (request.FlowDefinition != null)
                {
                    // Validate updated flow definition
                    var validationResult = await _flowBuilder.ValidateFlowAsync(request.FlowDefinition);
                    if (!validationResult.IsValid)
                    {
                        return BadRequest(new
                        {
                            message = "Flow validation failed",
                            errors = validationResult.Errors,
                            warnings = validationResult.Warnings
                        });
                    }

                    flowDoc.FlowDefinition = request.FlowDefinition;
                }

                var updatedFlow = await UpdateDBEntry(flowDoc, GetUpdateFields(request));
                if (updatedFlow == null)
                    return StatusCode(500, "Failed to update flow");

                return Ok(updatedFlow);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error updating flow: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete a flow.
        /// </summary>
        [HttpDelete("{flowId}")]
        [Authorize]
        public virtual async Task<IActionResult> DeleteFlow(string flowId)
        {
            try
            {
                var accountId = GetAccountId();
                if (string.IsNullOrEmpty(accountId))
                    return BadRequest("Account ID is required");

                var flowDoc = await GetDBEntry<FlowDocument>(accountId, flowId);
                if (flowDoc == null)
                    return NotFound("Flow not found");

                var deleted = await DeleteDBEntry<FlowDocument>(accountId, flowId);
                if (!deleted)
                    return StatusCode(500, "Failed to delete flow");

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error deleting flow: {ex.Message}");
            }
        }

        /// <summary>
        /// Execute a flow.
        /// </summary>
        [HttpPost("{flowId}/execute")]
        [Authorize]
        public virtual async Task<IActionResult> ExecuteFlow(string flowId, [FromBody] ExecuteFlowRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var accountId = GetAccountId();
                if (string.IsNullOrEmpty(accountId))
                    return BadRequest("Account ID is required");

                var flowDoc = await GetDBEntry<FlowDocument>(accountId, flowId);
                if (flowDoc == null)
                    return NotFound("Flow not found");

                if (!flowDoc.IsActive)
                    return BadRequest("Flow is not active");

                // Create execution context
                var context = new FlowExecutionContext
                {
                    SessionId = request.SessionId ?? Guid.NewGuid().ToString(),
                    FlowId = flowId,
                    AccountId = accountId,
                    InputFields = request.InputFields ?? new Dictionary<string, object>(),
                    VariableFields = new Dictionary<string, object>(),
                    UserId = GetClaimValue("UserId"),
                    Headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString())
                };

                // Register delivery endpoints if provided
                if (request.DeliveryEndpoints != null)
                {
                    foreach (var endpoint in request.DeliveryEndpoints)
                    {
                        await _resultDelivery.RegisterDeliveryEndpointAsync(context.SessionId, endpoint);
                    }
                }

                // Start execution
                if (request.IsAsync)
                {
                    var sessionId = await _flowEngine.StartFlowExecutionSessionAsync(flowDoc.FlowDefinition, context);
                    
                    return Accepted(new
                    {
                        sessionId = sessionId,
                        status = "started",
                        message = "Flow execution started asynchronously"
                    });
                }
                else
                {
                    var result = await _flowEngine.ExecuteFlowAsync(flowDoc.FlowDefinition, context);
                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error executing flow: {ex.Message}");
            }
        }

        /// <summary>
        /// Get execution status.
        /// </summary>
        [HttpGet("execution/{sessionId}/status")]
        [Authorize]
        public virtual async Task<IActionResult> GetExecutionStatus(string sessionId)
        {
            try
            {
                var state = await _flowEngine.GetExecutionStateAsync(sessionId);
                return Ok(state);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error retrieving execution status: {ex.Message}");
            }
        }

        /// <summary>
        /// Cancel flow execution.
        /// </summary>
        [HttpPost("execution/{sessionId}/cancel")]
        [Authorize]
        public virtual async Task<IActionResult> CancelExecution(string sessionId)
        {
            try
            {
                var cancelled = await _flowEngine.CancelExecutionAsync(sessionId);
                if (!cancelled)
                    return NotFound("Execution session not found or already completed");

                return Ok(new { message = "Execution cancelled successfully" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error cancelling execution: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate a flow definition.
        /// </summary>
        [HttpPost("validate")]
        [Authorize]
        public virtual async Task<IActionResult> ValidateFlow([FromBody] Flow flowDefinition)
        {
            try
            {
                var validationResult = await _flowBuilder.ValidateFlowAsync(flowDefinition);
                return Ok(validationResult);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error validating flow: {ex.Message}");
            }
        }

        /// <summary>
        /// Get available node types for this flow builder instance.
        /// </summary>
        [HttpGet("node-types")]
        [Authorize]
        public virtual async Task<IActionResult> GetNodeTypes()
        {
            try
            {
                var nodeTypes = _flowBuilder.GetRegisteredNodeTypes();
                var response = nodeTypes.Keys.Select(type => new
                {
                    type = type,
                    name = type,
                    description = $"{type} node"
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error retrieving node types: {ex.Message}");
            }
        }

        /// <summary>
        /// Get available task types for this flow builder instance.
        /// </summary>
        [HttpGet("task-types")]
        [Authorize]
        public virtual async Task<IActionResult> GetTaskTypes()
        {
            try
            {
                var taskTypes = _flowBuilder.GetRegisteredTaskTypes();
                var response = taskTypes.Keys.Select(type => new
                {
                    type = type,
                    name = type,
                    description = $"{type} task"
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error retrieving task types: {ex.Message}");
            }
        }

        /// <summary>
        /// Get the flow type for this controller instance. Override in derived classes.
        /// </summary>
        protected abstract string GetFlowType();

        /// <summary>
        /// Get the list of fields to update. Override in derived classes for custom behavior.
        /// </summary>
        protected virtual List<string> GetUpdateFields(UpdateFlowRequest request)
        {
            var fields = new List<string>();

            if (!string.IsNullOrEmpty(request.Name))
                fields.Add(nameof(FlowDocument.Name));

            if (!string.IsNullOrEmpty(request.Description))
                fields.Add(nameof(FlowDocument.Description));

            if (!string.IsNullOrEmpty(request.Version))
                fields.Add(nameof(FlowDocument.Version));

            if (request.IsActive.HasValue)
                fields.Add(nameof(FlowDocument.IsActive));

            if (request.FlowDefinition != null)
                fields.Add(nameof(FlowDocument.FlowDefinition));

            return fields;
        }
    }

    /// <summary>
    /// Request model for creating a flow.
    /// </summary>
    public class CreateFlowRequest
    {
        [Required]
        public string Name { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public string? Version { get; set; }

        public bool? IsActive { get; set; }

        [Required]
        public Flow FlowDefinition { get; set; } = new();
    }

    /// <summary>
    /// Request model for updating a flow.
    /// </summary>
    public class UpdateFlowRequest
    {
        public string? Name { get; set; }

        public string? Description { get; set; }

        public string? Version { get; set; }

        public bool? IsActive { get; set; }

        public Flow? FlowDefinition { get; set; }
    }

    /// <summary>
    /// Request model for executing a flow.
    /// </summary>
    public class ExecuteFlowRequest
    {
        public string? SessionId { get; set; }

        public Dictionary<string, object>? InputFields { get; set; }

        public bool IsAsync { get; set; } = false;

        public List<FlowDeliveryEndpoint>? DeliveryEndpoints { get; set; }
    }
}
